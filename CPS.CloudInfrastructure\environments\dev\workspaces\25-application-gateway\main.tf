data "azurerm_subnet" "appgw_subnet" {
  name                 = var.appgw_subnet_name
  virtual_network_name = "ar-az-est1-d-cps-vnet-001"
  resource_group_name  = "ar-az-est1-d-cps-rg-001"
}

# Use the existing module - SSL certificates managed manually in portal
module "application_gateway" {
  source = "../../../../modules/application-gateway"

  appgw_name          = var.application_gateway_name
  location            = var.location
  resource_group_name = var.resource_group_name
  public_ip_name      = var.public_ip_name
  sku_name            = var.sku_name
  sku_tier            = var.sku_tier
  min_capacity        = var.min_capacity
  max_capacity        = var.max_capacity
  subnet_id           = data.azurerm_subnet.appgw_subnet.id
  tags                = var.tags
  domains             = var.domains
  webapp_services     = {}

  # SSL certificates are managed manually in the portal
  enable_https     = false
  ssl_certificates = {}
  key_vault_id     = null
}

# Note: Key Vault references are not needed since SSL certificates and access policies
# are managed manually in the Azure portal

# Note: Key Vault access policies for SSL certificates are managed manually in Azure portal
# This allows the Application Gateway managed identity to access certificates from other environments:
# - QA Key Vault (ar-az-est1-q-cps-kv-001) for qa-app.ecps.ca and qa-supplier.ecps.ca certificates
# - SQA Key Vault (ar-az-est1-sqa-kv-001) for sqa-app.ecps.ca and sqa-supplier.ecps.ca certificates
# - DEV Key Vault access is managed by the module when key_vault_id is provided
