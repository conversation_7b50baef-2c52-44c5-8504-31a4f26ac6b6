data "azurerm_subnet" "appgw_subnet" {
  name                 = var.appgw_subnet_name
  virtual_network_name = "ar-az-est1-d-cps-vnet-001"
  resource_group_name  = "ar-az-est1-d-cps-rg-001"
}

# Application Gateway with full HTTPS configuration managed by Terraform
module "application_gateway" {
  source = "../../../../modules/application-gateway"

  appgw_name          = var.application_gateway_name
  location            = var.location
  resource_group_name = var.resource_group_name
  public_ip_name      = var.public_ip_name
  sku_name            = var.sku_name
  sku_tier            = var.sku_tier
  min_capacity        = var.min_capacity
  max_capacity        = var.max_capacity
  subnet_id           = data.azurerm_subnet.appgw_subnet.id
  tags                = var.tags
  domains             = var.domains
  webapp_services     = {}

  # Enable HTTPS with SSL certificates from Key Vault
  enable_https     = var.enable_https
  ssl_certificates = var.ssl_certificates
  key_vault_id     = data.azurerm_key_vault.dev.id
}

# Key Vault data sources for SSL certificate access
data "azurerm_key_vault" "dev" {
  name                = "ar-az-est1-d-cps-kv-001"
  resource_group_name = "ar-az-est1-d-cps-rg-001"
}

data "azurerm_key_vault" "qa" {
  name                = "ar-az-est1-q-cps-kv-001"
  resource_group_name = "ar-az-est1-q-cps-rg-001"
}

data "azurerm_key_vault" "sqa" {
  name                = "ar-az-est1-sqa-kv-001"
  resource_group_name = "ar-az-est1-sqa-cps-rg-001"
}

# Key Vault access policies for cross-environment SSL certificate access
# These allow the Application Gateway to access certificates from other environments
resource "azurerm_key_vault_access_policy" "appgw_qa_keyvault_access" {
  key_vault_id = data.azurerm_key_vault.qa.id
  tenant_id    = module.application_gateway.application_gateway_identity_tenant_id
  object_id    = module.application_gateway.application_gateway_identity_principal_id

  certificate_permissions = [
    "Get",
    "List"
  ]

  secret_permissions = [
    "Get",
    "List"
  ]
}

resource "azurerm_key_vault_access_policy" "appgw_sqa_keyvault_access" {
  key_vault_id = data.azurerm_key_vault.sqa.id
  tenant_id    = module.application_gateway.application_gateway_identity_tenant_id
  object_id    = module.application_gateway.application_gateway_identity_principal_id

  certificate_permissions = [
    "Get",
    "List"
  ]

  secret_permissions = [
    "Get",
    "List"
  ]
}
