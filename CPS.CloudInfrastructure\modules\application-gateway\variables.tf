variable "resource_group_name" {
  type        = string
  description = "Name of the resource group"
}

variable "public_ip_name" {
  type        = string
  description = "Name of the public IP address"
}

variable "location" {
  type        = string
  description = "Azure region where the virtual network will be created"
}

variable "appgw_name" {
  type        = string
  description = "Name of the Application Gateway"
}

variable "sku_name" {
  type        = string
  description = "Name of the subnet"
}

variable "sku_tier" {
  type        = string
  description = "CIDR block for the subnet (e.g., ********/24)"
}

variable "capacity" {
  type        = number
  description = "Capacity of the Application Gateway (used when not using autoscaling)"
  default     = null
}

variable "min_capacity" {
  type        = number
  description = "Minimum capacity for autoscaling"
  default     = 2
}

variable "max_capacity" {
  type        = number
  description = "Maximum capacity for autoscaling"
  default     = 10
}

variable "subnet_id" {
  type        = string
  description = "The ID of the subnet to which the Application Gateway will be associated"
}

variable "tags" {
  description = "Tags to apply to Application Insights"
  type        = map(string)
  default     = {}
}

variable "webapp_services" {
  description = "Map of webapp service names to their FQDNs (legacy - use domains instead)"
  type        = map(string)
  default     = {}
}

variable "domains" {
  description = "Map of domain names to their backend configurations"
  type = map(object({
    app_backend = object({
      host_name    = string
      type         = string # 'storage' or 'webapp'
      health_path  = optional(string, "/") # Default health check path for SPA
    })
    api_backend = object({
      host_name    = string
      type         = string # 'webapp'
      health_path  = optional(string, "/api/health") # Default health check path for API
    })
  }))
  default = {}
}

variable "enable_https" {
  description = "Enable HTTPS listeners and SSL certificates"
  type        = bool
  default     = false
}

variable "ssl_certificates" {
  description = "Map of SSL certificates from Key Vault"
  type = map(object({
    name                = string
    key_vault_secret_id = string
  }))
  default = {}
}

variable "key_vault_id" {
  description = "ID of the Key Vault for SSL certificate access (optional)"
  type        = string
  default     = null
}
