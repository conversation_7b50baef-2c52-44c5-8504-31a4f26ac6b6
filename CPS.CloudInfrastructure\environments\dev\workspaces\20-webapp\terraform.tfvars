subnet_id           = "/subscriptions/91c43315-5ccf-47d1-85dc-c73e8579b055/resourceGroups/ar-az-est1-d-cps-rg-001/providers/Microsoft.Network/virtualNetworks/ar-az-est1-d-cps-vnet-001/subnets/ar-az-est1-cps-sn-001"
service_plan_name   = "ar-az-est1-d-cps-asp-001" # App Service Plan name
sku_name            = "B2"
resource_group_name = "ar-az-est1-d-cps-rg-001"
location            = "eastus"
environment         = "dev"
os_type             = "Windows"
web_apps = [
  {
    name = "ar-az-est1-d-cpsadmin-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1dadminst001.z13.web.core.windows.net",
        "https://dev-app.ecps.ca",
        "http://localhost:5173",
        "http://localhost:5174"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://administrationportal/api"
    }
    sticky_settings = {
      app_setting_names = [
        "APPINSIGHTS_INSTRUMENTATIONKEY",
        "APPINSIGHTS_PROFILERFEATURE_VERSION",
        "APPINSIGHTS_SNAPSHOTFEATURE_VERSION",
        "ApplicationInsightsAgent_EXTENSION_VERSION",
        "DiagnosticServices_EXTENSION_VERSION",
        "EnabledApplicationInsightsLogger",
        "EnabledApplicationInsightsMetrics",
        "InstrumentationEngine_EXTENSION_VERSION",
        "SnapshotDebugger_EXTENSION_VERSION",
        "WEBSITE_TIME_ZONE",
        "XDT_MicrosoftApplicationInsights_BaseExtensions",
        "XDT_MicrosoftApplicationInsights_Mode",
        "XDT_MicrosoftApplicationInsights_NodeJS",
        "XDT_MicrosoftApplicationInsights_PreemptSdk"
      ]
    }
  },
  {
    name = "ar-az-est1-d-cpssupplier-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1dsupplierst001.z13.web.core.windows.net",
        "https://dev-supplier.ecps.ca",
        "http://localhost:5173",
        "http://localhost:5174"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://supplierportal/api"
    }
  }
]

storage_accounts = [
  {
    name                          = "arazest1dadminst001"
    resource_group_name           = "ar-az-est1-d-cps-rg-001"
    location                      = "eastus"
    account_tier                  = "Standard"
    static_website_index_document = "index.html"
    account_replication_type      = "LRS"
    tags = {
      accountnumber = "3682.301700"
      criticality   = "high"
      environment   = "dev"
      owner         = "<EMAIL>"
      profitcenter  = "*********"
      project       = "cps"
      terraform     = "true"
    }
    }, {
    name                          = "arazest1dsupplierst001"
    resource_group_name           = "ar-az-est1-d-cps-rg-001"
    location                      = "eastus"
    account_tier                  = "Standard"
    static_website_index_document = "index.html"
    account_replication_type      = "LRS"
    tags = {
      accountnumber = "3682.301700"
      criticality   = "high"
      environment   = "dev"
      owner         = "<EMAIL>"
      profitcenter  = "*********"
      project       = "cps"
      terraform     = "true"
    }
  },
  {
    name                     = "arazest1dfilesst001"
    resource_group_name      = "ar-az-est1-d-cps-rg-001"
    location                 = "eastus"
    account_tier             = "Standard"
    account_replication_type = "LRS"
    tags = {
      accountnumber = "3682.301700"
      criticality   = "high"
      environment   = "dev"
      owner         = "<EMAIL>"
      profitcenter  = "*********"
      project       = "cps"
      terraform     = "true"
    }
  }
]

tags = {
  accountnumber = "3682.301700"
  criticality   = "high"
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = "*********"
  project       = "cps"
  terraform     = "true"
}
