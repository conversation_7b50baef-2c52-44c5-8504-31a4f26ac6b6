resource_group_name            = "ar-az-est1-d-cps-rg-001"
location                       = "eastus"
function_app_name              = "ar-az-est1-d-cps-letsencrypt-func-001"
function_app_service_plan_name = "ar-az-est1-d-cps-letsencrypt-plan-001"
function_storage_account_name  = "arazest1dletsencrypt01"

# Azure resource references
key_vault_name            = "ar-az-est1-d-cps-kv-001"
application_insights_name = "ar-az-est1-d-cps-ai-001"
application_gateway_name  = "ar-az-est1-d-cps-agw-001"

# Let's Encrypt configuration
acme_directory_url       = "https://acme-staging-v02.api.letsencrypt.org/directory" # Use staging for testing
acme_email               = "<EMAIL>"
certificate_renewal_days = 30
domains                  = ["dev-app.ecps.ca", "dev-supplier.ecps.ca"]

# Map domains to their static website storage accounts for ACME challenges
static_website_storage_accounts = {
  "dev-app.ecps.ca"      = "arazest1dadminst001"
  "dev-supplier.ecps.ca" = "arazest1dsupplierst001"
}

tags = {
  accountnumber = "3682.301700"
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = "*********"
  project       = "cps"
  terraform     = "true"
}
