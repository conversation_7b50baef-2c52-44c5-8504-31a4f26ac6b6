{"version": 3, "terraform_version": "1.11.4", "backend": {"type": "azurerm", "config": {"access_key": null, "ado_pipeline_service_connection_id": null, "client_certificate": null, "client_certificate_password": null, "client_certificate_path": null, "client_id": null, "client_id_file_path": null, "client_secret": null, "client_secret_file_path": null, "container_name": "tfstate", "endpoint": null, "environment": null, "key": "terraform.tfstate", "lookup_blob_endpoint": null, "metadata_host": null, "msi_endpoint": null, "oidc_request_token": null, "oidc_request_url": null, "oidc_token": null, "oidc_token_file_path": null, "resource_group_name": "ar-az-est1-tfstate-rg", "sas_token": null, "snapshot": null, "storage_account_name": "arazest1tfstatedev", "subscription_id": null, "tenant_id": null, "use_aks_workload_identity": null, "use_azuread_auth": null, "use_cli": null, "use_msi": null, "use_oidc": null}, "hash": 18446744071751488530}}