variable "location" {
  description = "The Azure region where resources will be created"
  type        = string
  default     = "East US"
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "redis_capacity" {
  description = "The size of the Redis cache to deploy"
  type        = number
  default     = 0
}

variable "redis_family" {
  description = "The SKU family to use"
  type        = string
  default     = "C"
}

variable "redis_sku_name" {
  description = "The SKU of Redis to use"
  type        = string
  default     = "Standard"
}

variable "minimum_tls_version" {
  description = "The minimum supported TLS version"
  type        = string
  default     = "1.2"
}

variable "maxmemory_reserved" {
  description = "Value in megabytes reserved for non-cache usage"
  type        = number
  default     = 30
}

variable "maxmemory_delta" {
  description = "Value in megabytes reserved for non-cache usage"
  type        = number
  default     = 30
}

variable "tags" {
  description = "A mapping of tags to assign to the resource"
  type        = map(string)
  default     = {}
}
