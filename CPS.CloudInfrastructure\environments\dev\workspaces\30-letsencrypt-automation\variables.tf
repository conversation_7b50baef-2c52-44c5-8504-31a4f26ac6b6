variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "location" {
  description = "Azure region"
  type        = string
}

variable "function_app_name" {
  description = "Name of the Azure Function App for Let's Encrypt automation"
  type        = string
}

variable "function_app_service_plan_name" {
  description = "Name of the App Service Plan for the Function App"
  type        = string
}

variable "function_storage_account_name" {
  description = "Name of the storage account for the Function App"
  type        = string
}

variable "key_vault_name" {
  description = "Name of the Azure Key Vault for certificate storage"
  type        = string
}

variable "application_insights_name" {
  description = "Name of the Application Insights instance for monitoring"
  type        = string
}

variable "application_gateway_name" {
  description = "Name of the Application Gateway for certificate updates"
  type        = string
}

variable "acme_directory_url" {
  description = "ACME directory URL for Let's Encrypt"
  type        = string
  default     = "https://acme-staging-v02.api.letsencrypt.org/directory"
}

variable "acme_email" {
  description = "Email address for Let's Encrypt account registration"
  type        = string
}

variable "certificate_renewal_days" {
  description = "Number of days before expiration to renew certificates"
  type        = number
  default     = 30
}

variable "domains" {
  description = "List of domains to manage certificates for"
  type        = list(string)
  default     = []
}

variable "static_website_storage_accounts" {
  description = "Map of domain names to their static website storage account names for ACME challenges"
  type        = map(string)
  default     = {}
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
