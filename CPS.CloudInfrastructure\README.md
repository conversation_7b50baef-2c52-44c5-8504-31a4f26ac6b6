# CI/CD Pipeline for Terraform Infrastructure Management
An opinionated Azure Pipeline implementation for Terraform that supports multiple environments, workspaces, and CI/CD pipelines using terraform cloud.

## Overview

This project contains two separate pipelines:
- CI Pipeline (`azure-pipeline-ci.yml`): Triggered for each PR against the `main` branch
- CD Pipeline (`azure-pipeline-cd.yml`): Can only be invoked against the `main` branch and deploys changes selectively based on modified workspaces

# Project Structure
TODO: Add project tree here

## Setup Instructions

### 1. Create Service Principal
```bash
$ az ad sp create-for-rbac --name "ar-az-cps-dev" --role Contributor --scopes /subscriptions/91c43315-5ccf-47d1-85dc-c73e8579b055
```

### 2. Create a Team Token in Terraform Cloud.

### 3. Create projects in Terraform Cloud corresponding to each azure environment (dev, test, etc)

### 4. Create workspaces in Terraform Cloud with naming like <env-workspace> (for e.g. `dev-networking`)

### 5. Create a Variable Set in Terraform Cloud and define following variables
```bash
ARM_CLIENT_ID
ARM_CLIENT_SECRET
ARM_SUBSCRIPTION_ID
ARM_TENANT_ID
```

### 6. Assign Variable Set created above to appropriate Terraform Project (dev, test, etc)

### 7. Pipeline Configuration

Create the pipelines using the provided YAML files:
- `azure-pipeline-ci.yml`
- `azure-pipeline-cd.yml`

### 8. Branch Policies

Configure branch policies for the main branch:
1. Navigate to Repos > Branches
2. Select the main branch
3. Configure:
   - Build validation
   - Required reviewers
   - Minimum number of reviewers requirement

## Git Configuration
 
### Local Git Setup
Before starting development, configure your local Git settings to ensure correct author information:
 
```bash
# Set your name for this repository
git config user.name "Your Name"
 
# Set your email for this repository
git config user.email "<EMAIL>"
 
# Verify your settings
git config --list
```
 
Note: If you work with multiple clients or projects using different email addresses, it's recommended to set Git configuration at the repository level (as shown above) rather than globally. This prevents accidentally committing with the wrong email address.