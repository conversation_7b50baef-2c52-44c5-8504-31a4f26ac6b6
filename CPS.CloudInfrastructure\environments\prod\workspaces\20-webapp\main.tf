module "webapp" {
  source = "../../../../modules/webapp"

  subnet_id             = var.subnet_id
  web_apps              = var.web_apps
  resource_group_name   = var.resource_group_name
  location              = var.location
  app_service_plan_name = var.service_plan_name
  os_type               = var.os_type
  sku_name              = var.sku_name
  tags                  = var.tags

  app_settings = {
    "ENVIRONMENT"                     = var.environment,
    "WEBSITE_ENABLE_SYNC_UPDATE_SITE" = "true",
    "WEBSITE_RUN_FROM_PACKAGE"        = "1"
  }
}

module "storage" {
  source = "../../../../modules/storage"

  storage_accounts = var.storage_accounts
}
