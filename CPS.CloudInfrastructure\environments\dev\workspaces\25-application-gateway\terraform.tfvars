application_gateway_name = "ar-az-est1-d-cps-agw-001"
location                 = "eastus"
resource_group_name      = "ar-az-est1-d-cps-rg-001"
public_ip_name           = "ar-az-est1-d-cps-agw-pip-001"
sku_name                 = "WAF_v2"
sku_tier                 = "WAF_v2"
# Autoscaling configuration to match Azure portal
min_capacity      = 0  # Updated to match Azure portal (was 2)
max_capacity      = 2  # Updated to match Azure portal (was 10)
appgw_subnet_name = "ar-az-est1-cps-sn-002"

# Multi-environment domain configuration
# This single Application Gateway handles all non-production environments
domains = {
  # Development Environment
  "dev-app.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1dadminst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-d-cpsadmin-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/ping" # Updated to match Azure portal health probe
    }
  }
  "dev-supplier.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1dsupplierst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-d-cpssupplier-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/ping" # Updated to match Azure portal health probe
    }
  }

  # QA Environment
  "qa-app.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1qadminst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-q-cpsadmin-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/ping" # Updated to match Azure portal health probe
    }
  }
  "qa-supplier.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1qsupplierst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-q-cpssupplier-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/ping" # Updated to match Azure portal health probe
    }
  }

  # SQA Environment
  "sqa-app.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1sqaadminst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-sqa-cpsadmin-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/ping" # Updated to match Azure portal health probe
    }
  }
  "sqa-supplier.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1sqasupplier001.z13.web.core.windows.net"  # Fixed: removed 'st' to match Azure
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-sqa-cpssupplier-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/ping" # Updated to match Azure portal health probe
    }
  }
}

# Note: SSL certificates and HTTPS listeners are managed manually in Azure portal
# This approach keeps the module simple while allowing manual SSL certificate management
# The following SSL certificates are configured in the portal:
# - dev-app-ecps-ca (from ar-az-est1-d-cps-kv-001)
# - dev-supplier-ecps-ca (from ar-az-est1-d-cps-kv-001)
# - qa-app-ecps-ca (from ar-az-est1-q-cps-kv-001)
# - qa-supplier-ecps-ca (from ar-az-est1-q-cps-kv-001)
# - sqa-app-ecps-ca (from ar-az-est1-sqa-kv-001)
# - sqa-supplier-ecps-ca (from ar-az-est1-sqa-kv-001)

# Note: Health probe settings are configured in the module with these values:
# - Interval: 30 seconds (matches Azure portal)
# - Timeout: 30 seconds (Azure portal shows 120s - module needs update)
# - Unhealthy threshold: 3 (matches Azure portal)
# - Status codes: 200-399 (Azure portal shows 200-502 - module needs update)
# - Protocol: HTTPS (matches Azure portal)
# - Health paths: Configured per domain above (/ping for APIs, / for storage)

tags = {
  accountnumber = "3682.301700"
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = "*********"
  project       = "cps"
  terraform     = "true"
}
