resource "azurerm_public_ip" "appgw_pip" {
  name                = var.public_ip_name
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                 = "Standard"
  tags                = var.tags
}

# Create UserAssigned managed identity for Application Gateway
resource "azurerm_user_assigned_identity" "appgw_identity" {
  name                = "${var.appgw_name}-identity"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags                = var.tags
}

resource "azurerm_application_gateway" "appgw" {
  name                = var.appgw_name
  location            = var.location
  resource_group_name = var.resource_group_name
  tags                = var.tags

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.appgw_identity.id]
  }

  sku {
    name     = var.sku_name
    tier     = var.sku_tier
  }

  autoscale_configuration {
    min_capacity = var.min_capacity
    max_capacity = var.max_capacity
  }

  gateway_ip_configuration {
    name      = "appgw-ip-config"
    subnet_id = var.subnet_id
  }

  frontend_port {
    name = "httpPort"
    port = 80
  }

  frontend_port {
    name = "httpsPort"
    port = 443
  }

  frontend_ip_configuration {
    name                 = "frontendIP"
    public_ip_address_id = azurerm_public_ip.appgw_pip.id
  }

  # Backend pools for domain-based configuration
  dynamic "backend_address_pool" {
    for_each = var.domains
    content {
      name  = "app-pool-${replace(backend_address_pool.key, ".", "-")}"
      fqdns = [backend_address_pool.value.app_backend.host_name]
    }
  }

  dynamic "backend_address_pool" {
    for_each = var.domains
    content {
      name  = "api-pool-${replace(backend_address_pool.key, ".", "-")}"
      fqdns = [backend_address_pool.value.api_backend.host_name]
    }
  }

  # Legacy backend pools for backward compatibility (only when no domains are configured)
  dynamic "backend_address_pool" {
    for_each = length(var.domains) == 0 ? var.webapp_services : {}
    content {
      name  = "backendPool-${backend_address_pool.key}"
      fqdns = [backend_address_pool.value]
    }
  }

  # Backend HTTP settings for domain-based configuration - SPA (Storage)
  dynamic "backend_http_settings" {
    for_each = var.domains
    content {
      name                  = "app-settings-${replace(backend_http_settings.key, ".", "-")}"
      cookie_based_affinity = "Disabled"
      port                  = 443
      protocol              = "Https"
      request_timeout       = 20
      probe_name           = "app-probe-${replace(backend_http_settings.key, ".", "-")}"
      host_name            = backend_http_settings.value.app_backend.host_name
    }
  }

  # Backend HTTP settings for domain-based configuration - API (Web App)
  dynamic "backend_http_settings" {
    for_each = var.domains
    content {
      name                  = "api-settings-${replace(backend_http_settings.key, ".", "-")}"
      cookie_based_affinity = "Disabled"
      port                  = 443
      protocol              = "Https"
      request_timeout       = 20
      probe_name           = "api-probe-${replace(backend_http_settings.key, ".", "-")}"
      host_name            = backend_http_settings.value.api_backend.host_name
    }
  }

  # Legacy backend HTTP settings for backward compatibility (only when no domains are configured)
  dynamic "backend_http_settings" {
    for_each = length(var.domains) == 0 ? var.webapp_services : {}
    content {
      name                  = "httpSettings-${backend_http_settings.key}"
      cookie_based_affinity = "Disabled"
      port                  = 80
      protocol              = "Http"
      request_timeout       = 20
      probe_name           = "healthProbe-${backend_http_settings.key}"
    }
  }

  # Health probes for domain-based configuration - SPA (Storage)
  dynamic "probe" {
    for_each = var.domains
    content {
      name                = "app-probe-${replace(probe.key, ".", "-")}"
      protocol            = "Https"
      host                = probe.value.app_backend.host_name
      path                = probe.value.app_backend.health_path
      interval            = 30
      timeout             = 30
      unhealthy_threshold = 3
      match {
        status_code = ["200-399"]
      }
    }
  }

  # Health probes for domain-based configuration - API (Web App)
  dynamic "probe" {
    for_each = var.domains
    content {
      name                = "api-probe-${replace(probe.key, ".", "-")}"
      protocol            = "Https"
      host                = probe.value.api_backend.host_name
      path                = probe.value.api_backend.health_path
      interval            = 30
      timeout             = 30
      unhealthy_threshold = 3
      match {
        status_code = ["200-399"]
      }
    }
  }

  # Legacy health probes for backward compatibility (only when no domains are configured)
  dynamic "probe" {
    for_each = length(var.domains) == 0 ? var.webapp_services : {}
    content {
      name                = "healthProbe-${probe.key}"
      protocol            = "Http"
      host                = probe.value
      path                = "/"
      interval            = 30
      timeout             = 30
      unhealthy_threshold = 3
      match {
        status_code = ["200-399"]
      }
    }
  }

  # HTTP listeners for domain-based configuration (only when HTTPS is disabled)
  dynamic "http_listener" {
    for_each = var.enable_https ? {} : var.domains
    content {
      name                           = "listener-${replace(http_listener.key, ".", "-")}"
      frontend_ip_configuration_name = "frontendIP"
      frontend_port_name             = "httpPort"
      protocol                       = "Http"
      host_name                      = http_listener.key
    }
  }

  # Legacy HTTP listener for backward compatibility (only when no domains are configured)
  dynamic "http_listener" {
    for_each = length(var.webapp_services) > 0 && length(var.domains) == 0 ? { "default" = "default" } : {}
    content {
      name                           = "httpListener"
      frontend_ip_configuration_name = "frontendIP"
      frontend_port_name             = "httpPort"
      protocol                       = "Http"
    }
  }

  # SSL certificates from Key Vault
  dynamic "ssl_certificate" {
    for_each = var.ssl_certificates
    content {
      name                = ssl_certificate.value.name
      key_vault_secret_id = ssl_certificate.value.key_vault_secret_id
    }
  }

  # HTTPS listeners for domain-based configuration
  dynamic "http_listener" {
    for_each = var.enable_https ? var.domains : {}
    content {
      name                           = "https-listener-${replace(http_listener.key, ".", "-")}"
      frontend_ip_configuration_name = "frontendIP"
      frontend_port_name             = "httpsPort"
      protocol                       = "Https"
      host_name                      = http_listener.key
      ssl_certificate_name           = replace(http_listener.key, ".", "-")
    }
  }

  # URL path maps for domain-based routing
  dynamic "url_path_map" {
    for_each = var.domains
    content {
      name                               = "pathMap-${replace(url_path_map.key, ".", "-")}"
      default_backend_address_pool_name  = "app-pool-${replace(url_path_map.key, ".", "-")}"
      default_backend_http_settings_name = "app-settings-${replace(url_path_map.key, ".", "-")}"

      path_rule {
        name                       = "api-rule-${replace(url_path_map.key, ".", "-")}"
        paths                      = ["/api/*"]
        backend_address_pool_name  = "api-pool-${replace(url_path_map.key, ".", "-")}"
        backend_http_settings_name = "api-settings-${replace(url_path_map.key, ".", "-")}"
      }
    }
  }

  # Request routing rules for domain-based configuration (HTTP - only when HTTPS is disabled)
  dynamic "request_routing_rule" {
    for_each = var.enable_https ? {} : var.domains
    content {
      name                       = "rule-${replace(request_routing_rule.key, ".", "-")}"
      priority                   = 100 + (index(keys(var.domains), request_routing_rule.key) * 10)
      rule_type                  = "PathBasedRouting"
      http_listener_name         = "listener-${replace(request_routing_rule.key, ".", "-")}"
      url_path_map_name          = "pathMap-${replace(request_routing_rule.key, ".", "-")}"
    }
  }

  # HTTPS request routing rules for domain-based configuration
  dynamic "request_routing_rule" {
    for_each = var.enable_https ? var.domains : {}
    content {
      name                       = "https-rule-${replace(request_routing_rule.key, ".", "-")}"
      priority                   = 50 + (index(keys(var.domains), request_routing_rule.key) * 10)
      rule_type                  = "PathBasedRouting"
      http_listener_name         = "https-listener-${replace(request_routing_rule.key, ".", "-")}"
      url_path_map_name          = "pathMap-${replace(request_routing_rule.key, ".", "-")}"
    }
  }

  # Legacy request routing rules for backward compatibility (only when no domains are configured)
  dynamic "request_routing_rule" {
    for_each = length(var.domains) == 0 ? var.webapp_services : {}
    content {
      name                       = "routingRule-${request_routing_rule.key}"
      priority                   = 200 + index(keys(var.webapp_services), request_routing_rule.key)
      rule_type                  = "Basic"
      http_listener_name         = "httpListener"
      backend_address_pool_name  = "backendPool-${request_routing_rule.key}"
      backend_http_settings_name = "httpSettings-${request_routing_rule.key}"
    }
  }

  waf_configuration {
    enabled          = true
    firewall_mode    = "Prevention"
    rule_set_type    = "OWASP"
    rule_set_version = "3.2"
  }

  # Lifecycle rule to ignore manually managed resources
  # This prevents Terraform from trying to manage resources that are configured manually in the portal
  lifecycle {
    ignore_changes = [
      zones,                    # Azure may have zones configured but module doesn't support them
      ssl_certificate,          # SSL certificates are managed manually in the portal
      http_listener,           # HTTPS listeners are managed manually in the portal
      request_routing_rule,    # Routing rules using HTTPS listeners are managed manually
      backend_http_settings,   # Backend HTTP settings may be modified manually for HTTPS
      probe                    # Health probes may be modified manually
    ]
  }
}

# Key Vault access for Application Gateway managed identity (when Key Vault is provided)
resource "azurerm_key_vault_access_policy" "appgw_keyvault_access" {
  count        = var.key_vault_id != null ? 1 : 0
  key_vault_id = var.key_vault_id
  tenant_id    = azurerm_user_assigned_identity.appgw_identity.tenant_id
  object_id    = azurerm_user_assigned_identity.appgw_identity.principal_id

  certificate_permissions = [
    "Get",
    "List"
  ]

  secret_permissions = [
    "Get",
    "List"
  ]
}
